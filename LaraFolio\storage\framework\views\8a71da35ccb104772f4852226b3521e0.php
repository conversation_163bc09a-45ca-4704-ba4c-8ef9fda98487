
<?php $__env->startSection('content'); ?>
<section class="skills" id="skills">
    <div class="titlebar">
        <h1>Skills </h1>
        <button class="open-modal">New Skill</button>
    </div>
    <?php echo $__env->make('admin.skills.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="table">

        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <form method="GET" action="<?php echo e(route('admin.skills.index')); ?>" >
            <?php echo csrf_field(); ?>
            <div class="table-search">
                <div>
                    <select class="search-select" name="" id="">
                        <option value="">Filter Skills</option>
                    </select>
                </div>
                <div class="relative">
                    <input class="search-input" type="text" name="search" placeholder="Search Skill..." value="<?php echo e(request('search')); ?>">
                </div>
            </div>
        </form>

        <div class="skill_table-heading">
            <p>Name</p>
            <p>Proficiency</p>
            <p>Service</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
        <?php $__currentLoopData = $skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="skill_table-items">
            <p><?php echo e($skill->name); ?></p>
            <div class="table_skills-bar">
                <span class="table_skills-percentage" style="width: <?php echo e($skill->proficiency); ?>%;"></span>
                <strong><?php echo e($skill->proficiency); ?>%</strong>
            </div>
            <?php if($skill->service): ?>
                <p><?php echo e($skill->service->name); ?></p>
            <?php else: ?>
                <p></p>
            <?php endif; ?>
            
            <div>
                <button class="btn-icon success edit-skill-btn"
                        data-skill-id="<?php echo e($skill->id); ?>"
                        data-skill-name="<?php echo e($skill->name); ?>"
                        data-skill-proficiency="<?php echo e($skill->proficiency); ?>"
                        data-skill-service-id="<?php echo e($skill->service_id ?? ''); ?>">
                    <i class="fas fa-pencil-alt"></i>
                </button>
                
                <form method="POST" action="<?php echo e(route('admin.skills.destroy', $skill->id)); ?>" style="display: inline;">
                    <?php echo method_field('DELETE'); ?>
                    <?php echo csrf_field(); ?>
                    <button class="danger" class="far fa-trash-alt" onclick="return confirm('Are you sure you want to delete this skill?')">
                        <i class="far fa-trash-alt"></i>
                    </button>
                </form>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <div class="table-paginate">
            <?php echo e($skills->links('includes.pagination')); ?>

        </div>
    </div>
</section>

<!-- Edit Skill Modal -->
<div id="edit-skill-modal" style="display: none;">
    <?php
        $editSkill = new App\Models\Skill();
        $editSkill->id = 0; // Placeholder ID that will be updated by JavaScript
    ?>
    <?php echo $__env->make('admin.skills.edit', ['skill' => $editSkill], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/skills/index.blade.php ENDPATH**/ ?>