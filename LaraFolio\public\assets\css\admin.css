/*@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.13.1/css/all.min.css');*/
/* ==========================================================================
  Custom Properties bg rgba(241, 241, 241, 1);
========================================================================== */

:root {
  --body-color: rgb(246, 246, 247);
  --body-color-2: rgb(241, 241, 241);
  --primary-color: #FFF;
  --primary-color-dark: #f1f1f1;
  --text-dark: #000000;
  --text-light: var(rgb(32, 34, 35), #212b36);
  --font-sans: "Century Gothic", Tahoma, Arial, sans-serif;
  --font-serif: Georgia, Times, "Times New Roman", serif;
  --font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
}


/* ==========================================================================
  Sensible defaults
========================================================================== */

*,
*:before,
*:after {
  box-sizing: border-box;
}

::selection {
  background-color:  #6E57E0; 
  color: white;
}

html,
body {
  background: var(--body-color-2);
  color: var(--text-light);
  height: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 0.975rem;
  font-weight: 400;
  line-height: 1.25rem;
  letter-spacing: initial;
  font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
}

img,
picture {
  max-width: 100%;
  display: block;
}


/* ==========================================================================
  Typography
========================================================================== */

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-family);
  font-weight: 400;
  margin: 0;
}

h1 {
  font-size: 26px;
  line-height: 1;
}

h2 {
  font-size: 3rem;
  line-height: 1;
  margin: 1rem 0 1rem;
}

h3 {
  font-size: 2.25rem;
  line-height: 2.5rem;
  margin: 1rem 0 1rem;
}

h4 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  margin: 1rem 0 1rem;
}

h5 {
  font-size: 1.5rem;
  line-height: 2rem;
  margin: 1rem 0 0.5rem;
}

h6 {
  font-size: 1.25rem;
  line-height: 1.75rem;
  margin: 1rem 0 0.2rem;
}

p {
  color: #6a727a;
  font-weight: 500;
}

a {
  color: #6a727a;
}

a:hover {
  color: #6a727a;
}

hr {
  border: 0;
  height: 1px;
  background-color: rgb(152, 156, 160);
  margin-left: -1.25rem;
  margin-right: -1.25rem;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 400;
  margin: 0;
}

h1 {
  font-size: 26px;
  line-height: 1;
}

h2 {
  font-size: 18px;
}

h3 {
  font-size: 14px;
}

h4 {
  font-size: 14px;
}

h5 {
  font-size: 14px;
}

h6 {
  font-size: 14px;
}

p {
  margin: 0;
}

ul {
  list-style: disc inside none;
}

ol {
  list-style: decimal inside none;
}

ol[type="1"] {
  list-style: decimal inside none;
}

ol[type="a"] {
  list-style: lower-alpha inside none;
}

ol[type="A"] {
  list-style: upper-alpha inside none;
}

ol[type="i"] {
  list-style: lower-roman inside none;
}

ol[type="I"] {
  list-style: upper-roman inside none;
}

li {
  margin: 0;
}

dt {
  font-weight: bold;
}

dd {
  margin-left: 10px;
}

pre,
code {
  font-family: Monaco, Consolas, Lucida Console, monospace;
}

pre {
  font-size: 90%;
}

code {
  font-size: 85%;
  background: #ebeef0;
  padding: 2px;
  word-wrap: break-word;
  word-break: break-all;
  word-break: break-word;
}

input {
  font-size: 14px;
  line-height: 24px;
  font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
}

del {
  text-decoration: line-through;
}

address {
  font-style: normal;
}

small {
  color: #798c9c;
  font-size: 12px;
}


/* ==========================================================================
  Forms
========================================================================== */

form {
  font-family: var(--font-family);
  width: 100%;
}

@media screen and (min-width: 640px) {
  .next-input,
  .next-input--stylized {
      font-size: 0.875rem;
  }
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  padding: 0.6em 0.6em;
  font-family: var(--font-familly);
  font-size: 1em;
  width: 100%;
  color: rgb(32, 34, 35);
  margin-bottom: 15px;
  line-height: 24px;
  border-radius: 0.25rem;
  box-sizing: border-box;
  border: 1px solid rgb(186, 191, 195);
  border-top-color: rgb(140, 145, 150);
  background-color: rgb(255, 255, 255);
}

input[type="text"]:focus,
textarea:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
  border: 2px #000 solid;
  outline: 0;
}

input[type="text"],
input[type="email"],
input[type="password"],
button,
input[type="submit"],
input[type="reset"],
textarea,
select {
  border-radius: 0.25em;
}

label {
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.975rem;
  font-weight: 400;
  line-height: 1.25rem;
  text-transform: initial;
  letter-spacing: initial;
  cursor: pointer;
}

button,
input[type="submit"],
input[type="reset"] {
  margin-top: 0rem;
  border-radius: 8px;
  background: rgb(255, 255, 255);
  color: rgb(32, 34, 35);
  padding: 9px 12px;
  border: 1px solid rgb(186, 191, 195);
  border-top-color: rgb(201, 204, 207);
  border-bottom-color: rgb(186, 191, 196);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
  cursor: pointer;
  font-family: var(--font-family);
  font-size: 0.975rem;
}

button:not(:last-child),
input[type="submit"]:not(:last-child),
input[type="reset"]:not(:last-child) {
  margin-right: 0.2em;
}

button:hover,
input[type="submit"]:hover,
input[type="reset"]:hover {
  background: rgb(255,56,92);
}

input[type="checkbox"],
input[type="radio"] {
  margin-top: 1.4em;
  width: 1.4em;
  height: 1.4em;
}

input:focus {
color: rgb(30, 29, 29);  
}

input.visited {
color: rgb(30, 29, 29);  
}

/* ==========================================================================
  Lists
========================================================================== */


/* ==========================================================================
  Tables
========================================================================== */

table {
  border-collapse: collapse;
  width: 100%;
  background-color: var(--primary-color);
  color: #6a727a;
  margin-top: 20px;
}

th {
  /* background-color: var(--primary-color);
  color: var(--text-dark);*/
  color: #6a727a;
  font-size: 14px;
  font-weight: 500;
}

th,
td {
  border: 0px var(--primary-color-dark) solid;
  padding: 0.7em;
}

td:hover {
  cursor: pointer;
}

tr:hover,
tr:nth-child(odd):hover {
  background-color: #fdfdea;
}

tr:nth-child(odd) {
  background-color: #f3f3f3;
}


/* ==========================================================================
  Layout Elements
========================================================================== */

section {
  margin: 1.5rem 0;
}

footer {
  margin-top: 2em;
  justify-self: flex-end;
  width: 100%;
  font-family: var(--font-sans);
  font-size: 0.8em;
  text-align: center;
  background-color: var(--text-dark);
  color: var(--text-light);
  padding: 2em;
}


/* ==========================================================================
  Custom/Alternative Styles
========================================================================== */


/* ==========================================================================
  Custom/Alternative Styles for dashboard
========================================================================== */


/* Header Custom/Alternative Styles */

header>nav {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  position: fixed;
  z-index: 502;
  top: 0;
  right: 0;
  left: 0;
  height: 3.5rem;
  font-size: 1rem;
  line-height: 2;
  padding: 0;
  width: 100%;
  margin: auto;
  border-bottom: 1px solid var(--border);
  background-color: #6E57E0; 
  color: rgb(255, 255, 255);
  box-shadow: 0 1px 0 0 rgba(22, 29, 37, 0.05);
}

header>nav img {
  width: 11.625rem;
  line-height: 0;
  margin-left: 20px;
}

header>nav ul,
header>nav ol {
  align-content: space-around;
  align-items: center;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

header>nav ul li,
header>nav ol li {
  display: inline-block;
}

header>nav ul li a {
  display: inline-block;
}

.avatar-item {
  width: 36px;
  height: 36px;
  border: 1px solid rgb(201, 204, 207);
  border-radius: 50px;
  margin-right: 10px;
}

.avatar-img {
  width: 100%;
  margin: 0px;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
}

header>nav ul li span {
  display: inline-block;
  margin-right: 20px;
  cursor: pointer;
  font-size: 1rem;
}

.hr {
  
  margin-left: -0.65rem;
  margin-right: 0rem;
}
/*header profile nav*/

.show {
  display: block !important;
}

.header-profile-nav {
  z-index: 1;
  border: 1px solid rgb(201, 204, 207);
  border-radius: 8px;
  box-shadow: 0px 0px 5px rgba(23, 24, 24, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.15);
  position: fixed;
  display: none;
  top: 4rem;
  right: 1rem;
  width: 200px;
  background: #ffffff;
}

.header-profile-nav>span {
  z-index: 9999;
  position: absolute;
  top: -8px;
  right: 1.3rem;
  color: white;
}

.header-profile-nav>ul {
  list-style: none;
  width: 100%;
  padding-left: 10px;
  text-align: left;
}



.header-profile-nav>ul>li>a {
  margin: 0.2rem;
  list-style: none;
  text-decoration: none;
  font-size: 15px;
  font-weight: 500;
  color: #212b36;
  display: block;
  padding: 0.5rem;
 
}

.header-profile {
  cursor: pointer;
}


/* Sidebar Custom/Alternative Styles */

aside>nav {
  z-index: 1;
  top: 3.5rem;
  width: 15rem;
  position: fixed;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
  padding-left: 0px;
  background-color: rgb(235 235 235);
  border-right: 1px solid rgb(201, 204, 207);
}

.nav-wrapper {
  position: fixed;
  width: 15rem;
  height: 100%;
  background: var(--color-dark);
  border-right: 1.3px solid var(--border-right);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.nav__close {
  top: 13px;
  right: 13px;
  font-size: 2rem;
  cursor: pointer;
  color: #d63417;
  display: none;
}

.nav-active {
 
  border-radius: 4px;
  color: #6E57E0;
  border-left: 4px solid #6E57E0;
  background-color:#6e57e020;
}

.nav-active>span {
  color: #6E57E0;
}

.nav-list {
  position: sticky;
  z-index: 1;
  top: 4.5rem;
  bottom: 0px;
}

.nav-list>ul {
  font-size: 15px;
  list-style: none;
  line-height: 2rem;
  padding-left: 0px;
  margin-top: 0rem;
  
}

.nav-list>ul>li {
  margin-bottom: 0.5rem;
}

.nav-list>ul>li>a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  display: block;
  position: relative;
  padding: 5px 15px;
  margin-bottom: 5px;
}

.nav-list>ul>li>a:hover {
  color: #6E57E0;
  background-color: hsla(250, 92%, 85%, 0.416);
  margin-left: 0.2rem;
}

.nav-list>ul>li>a>span {
  padding: 0.5rem;
}

.sub_nav-list {
  list-style: none;
  padding-left: 2.4rem;
}

.sub_nav-list>li {
  margin-right: 10px;
  margin-bottom: 0.3rem;
}

.sub_nav-list>li>a {
  margin-right: 10px;
  text-decoration: none;
  display: block;
}

.sub_nav-list>li>a:hover,
.sub_nav-list>li>a:hover {
  color: #6E57E0;
  background-color: hsl(250deg 91.77% 84.91%);
}


/* Main content Custom/Alternative Styles */

main {
  display: block;
  padding-left:15rem;
  padding-top:3.5rem;
  padding-right: 0px;
  padding-bottom: 0px;
}

.main-content {
  box-sizing: border-box;
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-left: 230px;
  margin-top: 3.5rem;
  margin-bottom: 1rem;
}

.main-content>section {
  
  padding-top: 20px;
}








/*==================== REUSEABLE ====================*/
.avatar_img {
  width: 100%;
  height: 220px;
}
.flash_message {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  color: white;
  padding: 6px ;
  position:relative;
  background-color:#6E57E0;
  margin-bottom: 15px;
}

.alert {
  color: crimson;
  align-items: center;
}

.error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: -10px;
  margin-bottom: 10px;
  display: block;
}

/* Modal example Custom/Alternative Styles */
.hidden {
display: none;
}
.show {
  display: block !important;
}
.modal {
position: fixed;
inset: 0;
background: #0007;
z-index: 5000;
display: none;

}
.modal-content {
position: fixed;
top: 50vh;
left: 50vw;
transform: translate(-50%, -50%);
padding: 0.1rem;
border-radius: 0.8rem;
background-color: white;
margin: auto;
padding: 20px;
width: 600px;
box-shadow: 0 31px 41px 0 rgba(32, 42, 53, 0.2), 0 2px 16px 0 rgba(32, 42, 54, 0.08);

}

.modal-content>span {
color: #5a5454;
font-size: 28px;
font-weight: bold;
top: 15px;
right: 15px;
position: absolute;
cursor: pointer;
}

.modal-content>h2 {
  margin-bottom: 20px;
}

.modal-footer {
margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}


/* Product Custom Styles */

.table-product-head {
  padding: 0 30px;
  gap: 10px;
  display: grid;
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
  padding-top: 10px;
  margin-bottom: 10px;
  border-top: 1px solid #e0e0e0;
  grid-template-columns: 50px 1fr 120px 120px 120px;
  font-family: var(--font-family);
}

.table-product-body {
  padding: 10px 30px !important;
  gap: 10px;
  display: grid;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
  background-color: #f1f1f1;
  grid-template-columns: 50px 1fr 120px 120px 120px;
  font-family: var(--font-family);
}

.img-product {
  width: 100px;
  height: 80px;
}


/* Reduce nav side on mobile */

@media only screen and (max-width: 720px) {
  header>nav a {
      border: none;
      padding: 0;
      text-decoration: underline;
      line-height: 1;
  }
}


/*  Table Custom/Alternative Styles */

.titlebar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.titlebarfooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table {
  background-color: white;
  box-shadow: 0px 0px 5px rgba(23, 24, 24, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 0.05rem;
}

.table-filter {
  border-bottom: 1px solid #e0e0e0;
  padding: 0px;
  margin: 0px;
  font-family: var(--font-family);
}

.table-filter div {
  padding: 0 30px;
  margin: 0px
}

.table-filter-list {
  list-style: none;
  justify-content: flex-start;
  display: flex;
  padding: 0px;
  margin: 0px
}

.table-filter-link {
  margin-top: 8px;
  margin-bottom: 10px;
  cursor: pointer;
}

.link-active {
  color: #006fbb;
  font-weight: 500;
}

.table-search {
  padding: 0 30px;
  margin-top: 10px;
  display: grid;
  grid-template-columns: minmax(150px, auto) minmax(180px, 1fr);
}

.search-select {
  appearance: none;
  background: #eeeeee;
  color: #6a727a;
  width: 100%;
  border: none;
  border-top: 1px solid #e0e0e0;
  border-left: 1px solid #e0e0e0;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  margin-top: 3px;
  font-size: 1em;
}

.search-select-arrow {
  font-size: 1rem;
  opacity: 0.7;
  top: 8px;
  right: 20px;
  position: absolute;
}

.relative {
  position: relative;
}

.search-input-icon {
  top: 12px;
  left: 12px;
  color: #d4d4d4;
  position: absolute;
}

.search-input {
  width: 100%;
  border: none;
  color: #454f5b;
  border-top: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  padding-left: 40px;
  padding-right: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  font-family: var(--font-family);
  font-size: 1rem;
}


/*  Button Custom/Alternative Styles */

.btn-link {
  margin-top: 1em;
  border: 1px solid #e0e0e0;
  background: #e0e0e0;
  color: var(--text-dark);
  text-decoration: none;
  padding: 9px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-family: var(--font-family);
}

.btn-link:hover {
  background-color: var(--primary-color-dark);
}

.btn {
  border: 1px solid #e0e0e0;
  background: none;
  border-radius: 4px;
  color: #6a727a;
  cursor: pointer;
  font-family: var(--font-family);
}

.warning {
  background: rgb(255, 154, 19);
  color: white;
}

.secondary {
  background: linear-gradient(to bottom, #6E57E0, #5563c1);
  color: white;
}

.secondary:hover {
  background: linear-gradient(to bottom, #6371c7, #5563c1);
  color: white;
}

.success:hover {
  background: rgb(47, 214, 111);
  color: white;
}

.danger:hover {
  background: crimson;
  color: white;
}

.btn-icon {
  border: 1px solid #e0e0e0;
  background: none;
  border-radius: 4px;
  color: #6a727a;
  cursor: pointer;
  font-family: var(--font-family);
  padding: 8px 10px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.btn-icon.success {
  background: rgb(47, 214, 111);
  color: white;
  border-color: rgb(47, 214, 111);
}

.btn-icon.danger {
  background: crimson;
  color: white;
  border-color: crimson;
}

.btn-icon:hover {
  opacity: 0.8;
}


/*  Pagination Custom/Alternative Styles */

.table-paginate {
  align-items: center;
  justify-content: flex-end;;
  display: flex;
}

.pagination {
margin: 1rem;
padding-right: 15px;
justify-content: space-between;
color: #6a727a;
}

.pagination a {
color: black;
float: left;
padding: 6px 16px;
text-decoration: none;
}

.pagination a.active {
background-color: #e0e0e0;
color: #6a727a; 
}

.pagination a:hover {
background-color: #ddd;
color: gray;
}


/*  card Custom/Alternative Styles */

.ui-card {
  background-color: white;
  box-shadow: 0px 0px 5px rgba(23, 24, 24, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.card-wrapper {
  display: grid;
  grid-template-columns: 1fr minmax(auto, 290px);
  grid-gap: 10px;
  color: #6a727a;
  font-family: var(--font-family);
}

.social-wrapper {
  display: block;
  color: #6a727a;
  font-family: var(--font-family);
  margin: 1rem 0;
  width: 100%;
  max-width: none;
}

.card { 
  background-color: white;
  box-shadow: 0px 0px 5px rgba(12, 12, 12, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
  padding: 1.25rem;
  margin-bottom: 0.5rem;
  margin-right: 0.5rem;
  border:1px solid rgba(12, 12, 12, 0.05)
}

.alt {
  background-color: transparent;
  border: 2px var(--primary-color) solid;
  color: var(--primary-color);
}

.alt:hover {
  background-color: transparent;
  border: 2px var(--primary-color-dark) solid;
  color: var(--primary-color-dark);
}


/*==================== custom overview dashboard ====================*/
.overview{
  display: grid;
  grid-template-columns: 1fr minmax(auto, 30rem);
  padding: 0;
  margin: 1.5rem 1rem;
}

.overview_right{
  margin-left: 10px;
}

.overview_analyse{
  height: 240px;
  margin-top: 60px;
  margin-bottom: 20px;
  background-color: #FFFFFF;
}

.canvas#chart {
  background: var(--color-white);
  max-width: 100%;
  margin-top: 2rem;
  border-radius: var(--card-border-radius);
  padding: var(--card-padding);
}

.overview_skills{
  padding-top: 10px;
  margin-bottom: 10px;
}

.overview_skills-title{
  padding-bottom: 7px;
}

.skills_titles {
  display: flex;
  justify-content: space-between;
  text-transform: uppercase;
}

.skills_titles > span {
   padding-top: 10px;
}

.skills_data{
  margin-bottom: 5px;
}

.skills_bar,
.skills_percentage{
  height: 5px;
  border-radius: .25rem;
}

.skills_bar{
  background-color: hsl(250deg 91.77% 84.91%);
}

.skills_percentage{
  display: block;
  background-color: #6E57E0;
}

.overview_right{
  border-right: 1.3px solid var(--border-right);
}

.overview_cards{
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  grid-gap: 0px;
  padding: 10px;
  
}

.overview_cards-item{
  border: 1px solid #e0e0e0;
  
  padding: 20px;
  
}

.overview_data{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview_data p {
  text-transform: uppercase;
  font-size: 0.9rem;
}

.overview_data span{
  color: #1c2260;
  font-size: 1.7rem;
  font-weight: 700;
  margin: 0rem 0;
}

.overview_link{
  padding-top: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.overview_link>a{
  text-decoration: none;
  display: block;
}

.overview_table{
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px;
  padding: 10px;
  
  
}

.overview_table-header {
  display: grid;
  gap: 10px;
  grid-template-columns: 80px 150px 1fr;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-left:30px;
  padding-right: 30px;
  padding-top: 20px;
  padding-bottom: 1.2rem;
  background-color: #FFF;

}

.overview_table-items {
  padding: 10px 30px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 80px 150px 1fr;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  background-color: #FFF;
  
}

.overview_table-items>img{
  width: 100%;
  height: 60px;
}
/*==================== end custom about ====================*/

/*==================== custom ABOUT ====================*/

.about {
  max-width: 1068px;
  margin: 1.5rem auto;
}

.avatar_profile{
  display: flex;
  justify-content: center;
}

.avatar_profile_img{
  width: 200px;
  height: 200px;
  border: 50%;
}
/*==================== end custom ABOUT ====================*/ 

/*====================  custom social media ====================*/
.social_table-heading {
  padding: 5 5px;
  gap: 2px;
  display: grid;
  grid-template-columns: 300px 200fr 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  margin-top: 15px;
}

.social_table-items {
  padding: 5 5px !important;
  margin-top: 10px;
  gap: 2px;
  display: grid;
  grid-template-columns: 300px 200fr 100px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

/* Specific styling for the form inputs */
.social_table-items.form-row {
  grid-template-columns: 300px 200px 1fr;
}

.social_table-items>button{
  margin-top: -0.8rem;
}

.social_table-items.form-row>button{
  margin-top: 0;
  padding: 10px 16px;
  font-size: 14px;
  white-space: nowrap;
  justify-self: end;
  align-self: end;
  height: fit-content;
}

.social_table-items .input-container {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.social_table-items .input-container .error {
  color: #e74c3c;
  font-size: 12px;
  margin: 0 0 5px 0;
  padding: 0;
}

.social_table-items .input-container input {
  margin-bottom: 0;
}

.service_table-icon {
  background: #5563c1;
  color: #FFF;
    width: 35px;
    height: 35px;
    border-radius: 50px;
    align-items: center;
    border: 1px solid #e0e0e0;
}
/*==================== end custom social media ====================*/

/*==================== custom social SERVICES ====================*/
.services {
  margin: 1rem 2rem;
}

.service_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns: 200px 50px 1fr 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.service_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 200px 50px 1fr 120px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.service_table-icon {
  background: #5563c1;
  color: #FFF;
    width: 35px;
    height: 35px;
    border-radius: 50px;
    align-items: center;
    border: 1px solid #e0e0e0;
    padding: 0.5rem;
}
.show {
  display: block !important;
}
/*==================== end custom social SERVICES ====================*/ 

/*==================== custom social SKILLS ====================*/
.skills {
  margin: 1rem 2rem;
}

.skill_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns: 250px 1fr 200px 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.skill_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 250px 1fr 200px 110px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.table_skills-bar { 
  background-color: #6E57E0;
}

.table_skills-bar, .table_skills-percentage {
  height: 5px;
  border-radius: 0.25rem;
}

.table_skills-percentage {
  display: block;
  background-color: #0a0a12;
}
/*====================  end custom SKILLS ====================*/


/*====================  custom EDUCATIONS ====================*/
.educations{
  margin: 1rem 2rem;
}

.education_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.education_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 110px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}
/*====================  end custom EDUCATIONS ====================*/

/*==================== custom EXPERIENCE ====================*/
.experiences{
  margin: 1rem 2rem;
}
.experiences_container {
  margin-top: 1rem;
  margin: 0 auto;
  padding: 0 20px;
}
.experience_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.experience_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 120px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}
/*==================== end custom EXPERIENCE ====================*/ 

/*==================== USER ====================*/
.users {
  margin: 1rem 2rem;
}
.users_container {
  margin-top: 1rem;
  margin: 0 auto;
  padding: 0 20px;
}
.user_table-heading {
  padding: 0 20px;
  gap: 20px;
  display: grid;
  grid-template-columns:50px 1fr 1fr 1fr 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.user_table-items {
  padding: 5px 20px !important;
  gap: 20px;
  display: grid;
  grid-template-columns:50px 1fr 1fr 1fr 100px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.user_img-list {
  width: 100%;
  height: 50px;
  border:50px;
}

.add_testimonial-container {
  max-width: 968px;
  margin-top: 1rem;
  margin: 0 auto;
}

.testimonial_img-container{
  display: flex;
  justify-content: center;
}

.testimonial_img{
  width: 150px;
  height: 150px;
  border: 50px;
}
/*==================== end custom USER ====================*/

/*====================  custom PROJECT ====================*/
.projects {
  margin: 1rem 2rem;
}
.projects_container {
  margin-top: 1rem;
  margin: 0 auto;
  padding: 0 20px;
}
.project_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns:80px 250px 1fr 250px 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.project_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns:80px 250px 1fr 250px 110px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.project_img-list {
  width: 100%;
  height: 50px;
}

.add_project-container {
  max-width: 968px;
  margin-top: 1rem;
  margin: 0 auto;
}

.project_img-container{
  display: flex;
  justify-content: center;
}

.project_img{
  width: 100%;
  height: 200px;
}
/*====================  end custom PROJECT ====================*/

/*====================   custom TESTIMONIAL ====================*/
.testimonials {
  margin: 1rem 2rem;
}
.testimonials_container {
  margin-top: 1rem;
  margin: 0 auto;
  padding: 0 20px;
}
.testimonial_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns:50px 250px 200px 1fr 50px 100px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.testimonial_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns:50px 250px 200px 1fr 50px 100px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.testimonial_img-list {
  width: 100%;
  height: 50px;
  border:50px;
}

.add_testimonial-container {
  max-width: 968px;
  margin-top: 1rem;
  margin: 0 auto;
}

.testimonial_img-container{
  display: flex;
  justify-content: center;
}

.testimonial_img{
  width: 150px;
  height: 150px;
  border: 50px;
}
/*====================  end custom TESTIMONIAL ====================*/
/*=================== custom MESSAGES ====================*/
.messages_container {
  margin-top: 1rem;
  margin: 0 auto;
  padding: 0 20px;
}
.message_table-heading {
  padding: 0 20px;
  gap: 10px;
  display: grid;
  grid-template-columns: 250px 200px 300px 1fr 100px 50px;
  color: #808891;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 0.5rem;
  margin-top: 15px;
}

.message_table-items {
  padding: 5px 20px !important;
  gap: 10px;
  display: grid;
  grid-template-columns: 250px 200px 300px 1fr 100px 50px;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.6rem 0;
}

.badge_read {
  background: #6fc45f;
  color: white;
  border-radius: 5px;
  text-align: center;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
}

.badge_unread {
  background: #d68a17;
  color: white;
  border-radius: 5px;
  text-align: center;
  padding-left: 10px;
  padding-right: 10px;
  cursor: pointer;
}
/*==================== end custom MESSAGES ====================*/

/*==================== custom ACCOUNTS ====================*/
.account-body{
background-color: linear-gradient(310deg, #B08FFF 0%, #6031D2 100%);
position: fixed;
width: 100%;
height: 100%;
margin-left: -15rem;
margin-top: -3.5rem;
overflow: hidden;
}
.account-container{
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
min-height: 100vh;
background: linear-gradient(310deg, #B08FFF 0%, #6031D2 100%);
margin-left: -15rem;
margin-top: -3.5rem;

}
.account-card{
background-color: rgb(246, 246, 247);
margin-left: 10vw;
margin-right: 10vw;
width:550px;
padding:40px;
border-radius: 0.5rem;
box-shadow: 0 31px 41px 0 rgba(32, 42, 53, 0.2), 0 2px 16px 0 rgba(32, 42, 54, 0.08);
}
.account-card>header{
text-align: center;
}
.account-card>form>button{
width: 100%;
background:#303030; 
color:#FFFFFF;
padding:12px;
} 
/*==================== end custom ACCOUNTS ====================*/



/*==================== custom NOTFOUND ====================*/
.notfound-body{
  background: #FFF;
  color:#000;
  margin-left: -15rem;
  margin-top: -3.5rem;
  overflow: hidden;
}
.notfound-container{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
.notfound-card{
  text-align: center;
  font-size: 20px;
  margin-left: 10vw;
  margin-right: 10vw;
  width:550px;
  padding:40px;
}

.notfound-items {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px
}
/*==================== end custom NOTFOUND ====================*/

/*==================== custom PRELOAD ====================*/
.preloader {
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
opacity: 95%;
}

.loader {
  width: 48px;
  height: 48px;
  border: 5px solid #6E57E0;
  border-bottom-color: #89a4ff;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
      transform: rotate(0deg);
  }
  100% {
      transform: rotate(360deg);
  }
}
/*==================== end custom PRELOAD ====================*/

/*==================== SEETING====================*/

  .setting{
        margin: 1.5rem auto;
        max-width: 1350px;
        
    }

    .setting-titlebar{  
       
        display: flex;
        align-items: center;
        padding-bottom:8px;
        margin-bottom: 0px;
        background-color: rgb(241, 241, 241);;
    }

    .setting-avatar{
        width: 60px;
        border-radius: 60px;
        padding:4px;
    }

    .setting-wrapper {
        display: grid;
        grid-template-columns: 1fr minmax(auto, 66rem);
        grid-gap: 10px;
        
    }

.nav-setting-wrapper {
    position: fixed;
    width:19rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin:0rem;
}

.nav-list-item-setting{
  margin-top: -1px;
  margin-bottom: -1px;
}

.setting-link{
    padding: 5px 5px;
    margin-bottom: 0px;
}

.setting-nav{
    background-color: white;
}

.setting_nav{
   background-color: white;
    box-shadow: 0px 0px 5px rgba(12, 12, 12, 0.05), 0px 1px 2px rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    margin-right: 0.8rem;
    border: 1px solid rgba(12, 12, 12, 0.05);
}

/*====================END SEETING====================*/


/* ==========================================================================
  Media queries
========================================================================== */

@media screen and (max-width: 550px) {
  button,
  input[type="submit"],
  input[type="reset"] {
      width: 100%;
  }
}